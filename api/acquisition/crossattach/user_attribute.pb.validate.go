// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/acquisition/crossattach/user_attribute.proto

package crossattach

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	lendability "github.com/epifi/gamma/api/preapprovedloan/lendability"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = lendability.LoanAffinityCategory(0)
)

// Validate checks the field values on UserAttributeValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserAttributeValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserAttributeValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserAttributeValueMultiError, or nil if none found.
func (m *UserAttributeValue) ValidateAll() error {
	return m.validate(true)
}

func (m *UserAttributeValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Value.(type) {
	case *UserAttributeValue_ScreenerCheckResult:
		if v == nil {
			err := UserAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ScreenerCheckResult
	case *UserAttributeValue_LoanAffinityCategory:
		if v == nil {
			err := UserAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for LoanAffinityCategory
	case *UserAttributeValue_PdCategory:
		if v == nil {
			err := UserAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PdCategory
	case *UserAttributeValue_HomeLandedTimestamp:
		if v == nil {
			err := UserAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHomeLandedTimestamp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserAttributeValueValidationError{
						field:  "HomeLandedTimestamp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserAttributeValueValidationError{
						field:  "HomeLandedTimestamp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHomeLandedTimestamp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserAttributeValueValidationError{
					field:  "HomeLandedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UserAttributeValue_LatestAssetConnectedTimestamp:
		if v == nil {
			err := UserAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLatestAssetConnectedTimestamp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserAttributeValueValidationError{
						field:  "LatestAssetConnectedTimestamp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserAttributeValueValidationError{
						field:  "LatestAssetConnectedTimestamp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLatestAssetConnectedTimestamp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserAttributeValueValidationError{
					field:  "LatestAssetConnectedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UserAttributeValue_WealthBuilderConnected:
		if v == nil {
			err := UserAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for WealthBuilderConnected
	case *UserAttributeValue_NetWorthAssetValueList_:
		if v == nil {
			err := UserAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetWorthAssetValueList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserAttributeValueValidationError{
						field:  "NetWorthAssetValueList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserAttributeValueValidationError{
						field:  "NetWorthAssetValueList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetWorthAssetValueList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserAttributeValueValidationError{
					field:  "NetWorthAssetValueList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UserAttributeValueMultiError(errors)
	}

	return nil
}

// UserAttributeValueMultiError is an error wrapping multiple validation errors
// returned by UserAttributeValue.ValidateAll() if the designated constraints
// aren't met.
type UserAttributeValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserAttributeValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserAttributeValueMultiError) AllErrors() []error { return m }

// UserAttributeValueValidationError is the validation error returned by
// UserAttributeValue.Validate if the designated constraints aren't met.
type UserAttributeValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserAttributeValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserAttributeValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserAttributeValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserAttributeValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserAttributeValueValidationError) ErrorName() string {
	return "UserAttributeValueValidationError"
}

// Error satisfies the builtin error interface
func (e UserAttributeValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserAttributeValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserAttributeValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserAttributeValueValidationError{}

// Validate checks the field values on
// UserAttributeValue_NetWorthAssetValueList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserAttributeValue_NetWorthAssetValueList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UserAttributeValue_NetWorthAssetValueList with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UserAttributeValue_NetWorthAssetValueListMultiError, or nil if none found.
func (m *UserAttributeValue_NetWorthAssetValueList) ValidateAll() error {
	return m.validate(true)
}

func (m *UserAttributeValue_NetWorthAssetValueList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNetWorthAssetValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserAttributeValue_NetWorthAssetValueListValidationError{
						field:  fmt.Sprintf("NetWorthAssetValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserAttributeValue_NetWorthAssetValueListValidationError{
						field:  fmt.Sprintf("NetWorthAssetValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserAttributeValue_NetWorthAssetValueListValidationError{
					field:  fmt.Sprintf("NetWorthAssetValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UserAttributeValue_NetWorthAssetValueListMultiError(errors)
	}

	return nil
}

// UserAttributeValue_NetWorthAssetValueListMultiError is an error wrapping
// multiple validation errors returned by
// UserAttributeValue_NetWorthAssetValueList.ValidateAll() if the designated
// constraints aren't met.
type UserAttributeValue_NetWorthAssetValueListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserAttributeValue_NetWorthAssetValueListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserAttributeValue_NetWorthAssetValueListMultiError) AllErrors() []error { return m }

// UserAttributeValue_NetWorthAssetValueListValidationError is the validation
// error returned by UserAttributeValue_NetWorthAssetValueList.Validate if the
// designated constraints aren't met.
type UserAttributeValue_NetWorthAssetValueListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserAttributeValue_NetWorthAssetValueListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserAttributeValue_NetWorthAssetValueListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserAttributeValue_NetWorthAssetValueListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserAttributeValue_NetWorthAssetValueListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserAttributeValue_NetWorthAssetValueListValidationError) ErrorName() string {
	return "UserAttributeValue_NetWorthAssetValueListValidationError"
}

// Error satisfies the builtin error interface
func (e UserAttributeValue_NetWorthAssetValueListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserAttributeValue_NetWorthAssetValueList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserAttributeValue_NetWorthAssetValueListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserAttributeValue_NetWorthAssetValueListValidationError{}
