package cohort

import (
	"context"
	"time"

	"github.com/google/wire"
)

var (
	CohortHelperWireSet = wire.NewSet(
		NewCohortWbConnected,
		NewCohortWbConnectedAssetValueBelowThreshold,
		NewCohortWbNotConnected)
)

//go:generate mockgen -source=helper.go -destination=./mocks/helper_mocks.go -package=mocks
type CohortHelper interface {
	IsMember(context.Context, *IsMemberRequest) (*IsMemberResponse, error)
	CanUpsell(context.Context, *CanUpsellRequest) (*CanUpsellResponse, error)
}

type IsMemberRequest struct {
	ActorId    string
	CohortType string
}

type IsMemberResponse struct {
	IsMember bool
}

type CanUpsellRequest struct {
	ActorId string
}

type CanUpsellResponse struct {
	CanUpsell   bool
	UpsellAfter time.Duration
}

func (r *IsMemberRequest) GetActorId() string {
	if r != nil {
		return r.ActorId
	}
	return ""
}

func (r *IsMemberRequest) GetCohortType() string {
	if r != nil {
		return r.CohortType
	}
	return ""
}

func (r *IsMemberResponse) GetIsMember() bool {
	if r != nil {
		return r.IsMember
	}
	return false
}

func (r *CanUpsellRequest) GetActorId() string {
	if r != nil {
		return r.ActorId
	}
	return ""
}

func (r *CanUpsellResponse) GetCanUpsell() bool {
	if r != nil {
		return r.CanUpsell
	}
	return false
}

func (r *CanUpsellResponse) GetUpsellAfter() time.Duration {
	if r != nil {
		return r.UpsellAfter
	}
	return 0
}
