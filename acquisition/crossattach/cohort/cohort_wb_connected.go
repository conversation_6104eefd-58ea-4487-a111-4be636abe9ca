package cohort

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
)

type CohortWbConnected struct {
	userAttributesHelper userattributes.UserAttributesHelper
}

func NewCohortWbConnected(userAttributesHelper userattributes.UserAttributesHelper) *CohortWbConnected {
	return &CohortWbConnected{
		userAttributesHelper: userAttributesHelper,
	}
}

func (s *CohortWbConnected) IsMember(ctx context.Context, req *IsMemberRequest) (*IsMemberResponse, error) {
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if !isWBConnectedUser(getUserAttributeRes.GetUserAttributeValue().GetNetWorthAssetValueList()) {
		logger.Info(ctx, "User is not a WB connected user")
		return &IsMemberResponse{
			IsMember: false,
		}, nil
	}

	return &IsMemberResponse{
		IsMember: true,
	}, nil
}

func (s *CohortWbConnected) CanUpsell(ctx context.Context, req *CanUpsellRequest) (*CanUpsellResponse, error) {
	// can upsell after 7 days from latest asset connected date
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	latestAssetConnectedTimestamp := getUserAttributeRes.GetUserAttributeValue().GetLatestAssetConnectedTimestamp()
	if latestAssetConnectedTimestamp == nil {
		logger.Error(ctx, "empty latest asset connected timestamp")
		return nil, fmt.Errorf("empty latest asset connected timestamp")
	}
	upsellAfter := 7 * 24 * time.Hour
	if time.Since(latestAssetConnectedTimestamp.AsTime()) < upsellAfter {
		logger.Info(ctx, "cannot upsell as landed on home within 7 days")
		return &CanUpsellResponse{
			CanUpsell:   false,
			UpsellAfter: latestAssetConnectedTimestamp.AsTime().Add(upsellAfter).Sub(time.Now()),
		}, nil
	}

	return &CanUpsellResponse{
		CanUpsell:   true,
		UpsellAfter: 0,
	}, nil
}
