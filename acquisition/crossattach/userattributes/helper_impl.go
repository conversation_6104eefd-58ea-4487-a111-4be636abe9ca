package userattributes

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes/dao"
	"github.com/epifi/gamma/api/acquisition/crossattach"
)

type UserAttributesHelperImpl struct {
	// V1 : add a map later to cache common attributes for a request
	UserAttributesDao  dao.UserAttributesDao
	AttributeGetterMap map[crossattach.UserAttribute]UserAttributesGetter
}

func NewUserAttributesHelperImpl(userAttributesDao dao.UserAttributesDao,
	lendabilityDetailsGetter *LendabilityDetailsGetter,
	screenerCheckResultGetter *ScreenerCheckResultGetter,
	onboardingDetailsGetter *OnboardingDetailsGetter,
	netWorthAssetValuesGetter *NetWorthAssetValuesGetter,
	latestAssetConnectedTimestampGetter *LatestAssetConnectedTimestampGetter) *UserAttributesHelperImpl {
	return &UserAttributesHelperImpl{
		UserAttributesDao: userAttributesDao,
		AttributeGetterMap: map[crossattach.UserAttribute]UserAttributesGetter{
			crossattach.UserAttribute_USER_ATTRIBUTE_PD_CATEGORY:                      lendabilityDetailsGetter,
			crossattach.UserAttribute_USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY:           lendabilityDetailsGetter,
			crossattach.UserAttribute_USER_ATTRIBUTE_SCREENER_CHECK_RESULT:            screenerCheckResultGetter,
			crossattach.UserAttribute_USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP:            onboardingDetailsGetter,
			crossattach.UserAttribute_USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST:      netWorthAssetValuesGetter,
			crossattach.UserAttribute_USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP: latestAssetConnectedTimestampGetter,
		},
	}
}

var _ UserAttributesHelper = &UserAttributesHelperImpl{}

func (s *UserAttributesHelperImpl) GetUserAttribute(ctx context.Context, req *GetUserAttributeRequest) (*GetUserAttributeResponse, error) {
	/*
		V1 (post observing performance):
			Cache attributes in a map within a request
	*/
	if req.GetUserAttribute() == crossattach.UserAttribute_USER_ATTRIBUTE_UNSPECIFIED || req.GetActorId() == "" {
		logger.Info(ctx, "invalid arguments in GetUserAttribute")
		return nil, fmt.Errorf("invalid user attribute")
	}
	if !req.GetForceRefresh() {
		userAttributeValue, err := s.UserAttributesDao.GetUserAttribute(ctx, req.GetActorId(), req.GetUserAttribute())
		if err == nil {
			return &GetUserAttributeResponse{
				UserAttributeValue: userAttributeValue,
			}, nil
		}

		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in getting user attribute from cache", zap.Error(err))
			// ignoring error from cache and getting data from actual source
		}
	}

	attributeGetter, ok := s.AttributeGetterMap[req.UserAttribute]
	if !ok {
		logger.Error(ctx, "attribute fetcher not found for user attribute", zap.String("user_attribute", req.UserAttribute.String()))
		return nil, fmt.Errorf("attribute fetcher not found for user attribute %s", req.UserAttribute.String())
	}

	res, err := attributeGetter.GetUserAttributesFromSource(ctx, &GetUserAttributesFromSourceRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: req.GetUserAttribute(),
	})
	if err != nil {
		logger.Error(ctx, "error in fetching user attribute from source", zap.Error(err))
		return nil, err
	}

	if err = s.UserAttributesDao.SetUserAttributes(ctx, req.GetActorId(), res.GetUserAttributeValueMap()); err != nil {
		logger.Error(ctx, "error in setting user attributes in cache", zap.Error(err))
		// ignoring error as this can be best effort
	}
	return &GetUserAttributeResponse{
		UserAttributeValue: res.GetUserAttributeValueMap()[req.GetUserAttribute()],
	}, nil
}
