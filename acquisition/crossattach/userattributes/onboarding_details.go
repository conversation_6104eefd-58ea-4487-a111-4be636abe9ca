package userattributes

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/acquisition/crossattach"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

type OnboardingDetailsGetter struct {
	onbClient onbPb.OnboardingClient
}

func NewOnboardingDetailsGetter(onbClient onbPb.OnboardingClient) *OnboardingDetailsGetter {
	return &OnboardingDetailsGetter{
		onbClient: onbClient,
	}
}

var _ UserAttributesGetter = (*OnboardingDetailsGetter)(nil)

func (s *OnboardingDetailsGetter) GetUserAttributesFromSource(ctx context.Context, req *GetUserAttributesFromSourceRequest) (*GetUserAttributesFromSourceResponse, error) {
	onbDetailsRes, err := s.onbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    req.GetActorId(),
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(onbDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching onboarding details", zap.Error(rpcErr))
		return nil, rpcErr
	}
	return &GetUserAttributesFromSourceResponse{
		UserAttributeValueMap: map[crossattach.UserAttribute]*crossattach.UserAttributeValue{
			crossattach.UserAttribute_USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP: {
				Value: &crossattach.UserAttributeValue_HomeLandedTimestamp{
					// Using fi lite enabled as source for home landed timestamp
					HomeLandedTimestamp: onbDetailsRes.GetDetails().GetFiLiteDetails().GetAccessibilityEnabledAt(),
				},
			},
		},
	}, nil
}
